# **Typing Utility Functions & Advanced Patterns**

This guide explores how to apply TypeScript effectively to utility functions and more advanced type manipulation patterns, promoting DRY and robust code across our Ultimate Electrical Designer.

## **1. Writing Generic Utility Functions (src/utils/)**

Generic utility functions are stateless, pure functions that can operate on various data types while maintaining type safety.

- **What it is:** Functions that use type parameters (generics) to define their inputs and outputs flexibly.  
  // src/utils/arrayUtils.ts (Example)  
  // Generic function to filter unique items based on a key  
  function getUniqueItemsByKey\<T\>(items: T\[\], key: keyof T): T\[\] {  
  const seen = new Set\<any\>(); // Using any for simplicity with Set, could be more strict  
  return items.filter(item =\> {  
  const value = item\[key\];  
  if (seen.has(value)) {  
  return false;  
  }  
  seen.add(value);  
  return true;  
  });  
  }  
    
  interface CableComponent {  
  id: string;  
  name: string;  
  manufacturer: string;  
  }  
    
  const cables: CableComponent\[\] = \[  
  { id: 'c1', name: '<PERSON> A', manufacturer: '<PERSON>uf1' },  
  { id: 'c2', name: '<PERSON> B', manufacturer: 'Manuf2' },  
  { id: 'c3', name: 'Cable A', manufacturer: 'Manuf1' }, // Duplicate  
  \];  
    
  const uniqueCables = getUniqueItemsByKey(cables, 'name');  
  // Type of uniqueCables is CableComponent\[\]

- **Why it helps:** Promotes DRY by creating reusable functions that work across different data structures. Generics ensure type safety regardless of the specific data types passed in.

## **2. Implementing Custom Type Guards**

Type guards are functions that tell TypeScript how to narrow down a type within a conditional block.

- **What it is:** A function that returns a boolean, and its return type is a "type predicate" (e.g., value is Type).  
  // src/utils/typeGuards.ts (Example)  
  import { ErrorResponseSchema } from 'types/api'; // Our backend error schema  
    
  // Type guard for checking if an object is a specific Project type  
  interface ProjectData { id: string; name: string; status: string; }  
  function isProjectData(obj: any): obj is ProjectData {  
  return (  
  typeof obj === 'object' && obj !== null &&  
  'id' in obj && typeof obj.id === 'string' &&  
  'name' in obj && typeof obj.name === 'string' &&  
  'status' in obj && typeof obj.status === 'string'  
  );  
  }  
    
  // Type guard for our backend error response  
  function isBackendErrorResponse(error: unknown): error is ErrorResponseSchema {  
  // This is the same function from the Data Fetching guide, good for reuse!  
  return (  
  typeof error === 'object' &&  
  error !== null &&  
  'detail' in error &&  
  typeof (error as ErrorResponseSchema).detail === 'string' &&  
  ('code' in error ? typeof (error as ErrorResponseSchema).code === 'string' : true) &&  
  ('field_errors' in error ? typeof (error as ErrorResponseSchema).field_errors === 'object' \|\| (error as ErrorResponseSchema).field_errors === null : true)  
  );  
  }

- **Why it helps:** Allows for safe and intelligent handling of polymorphic data (data that can be one of several types), reducing the need for any and improving code clarity in conditional logic.

## **3. Leveraging TypeScript Utility Types**

TypeScript provides built-in "utility types" to transform existing types.

- **Partial\<T\>**: Makes all properties in T optional.  
  interface ProjectSettings {  
  theme: 'dark' \| 'light';  
  notificationsEnabled: boolean;  
  defaultView: string;  
  }  
    
  // When updating settings, not all fields might be provided  
  type PartialProjectSettings = Partial\<ProjectSettings\>;  
  // { theme?: 'dark' \| 'light'; notificationsEnabled?: boolean; defaultView?: string; }

- **Required\<T\>**: Makes all properties in T required.  
  type AllRequiredProjectSettings = Required\<PartialProjectSettings\>;  
  // { theme: 'dark' \| 'light'; notificationsEnabled: boolean; defaultView: string; }

- **Pick\<T, K\>**: Constructs a type by picking the set of properties K from T.  
  type ProjectCardProps = Pick\<ProjectReadSchema, 'id' \| 'name' \| 'status'\>;  
  // { id: string; name: string; status: 'Active' \| 'Completed' \| 'Pending'; }

- **Omit\<T, K\>**: Constructs a type by omitting the set of properties K from T.  
  type ProjectCreationPayload = Omit\<ProjectReadSchema, 'id' \| 'createdAt' \| 'updatedAt' \| 'status'\>;  
  // { name: string; description?: string; /\* ... other creatable fields \*/ }

- **Exclude\<T, U\>**: Constructs a type by excluding from T all union members that are assignable to U.  
  type AllProjectStatuses = 'Active' \| 'Completed' \| 'Pending' \| 'Archived';  
  type NonPendingStatuses = Exclude\<AllProjectStatuses, 'Pending'\>;  
  // 'Active' \| 'Completed' \| 'Archived'

- **Extract\<T, U\>**: Constructs a type by extracting from T all union members that are assignable to U.  
  type CommonComponentTypes = 'Cable' \| 'Breaker' \| 'Pipe' \| 'Heater' \| 'Sensor';  
  type ElectricalComponents = Extract\<CommonComponentTypes, 'Cable' \| 'Breaker' \| 'Heater' \| 'Sensor'\>;  
  // 'Cable' \| 'Breaker' \| 'Heater' \| 'Sensor'

- **NonNullable\<T\>**: Excludes null and undefined from T.  
  type UserID = string \| null \| undefined;  
  type ValidUserID = NonNullable\<UserID\>; // string

- **Why it helps:** Promotes DRY by allowing you to derive new types from existing ones, rather than manually redefining them. This is incredibly powerful for maintaining consistency and reducing boilerplate in your type definitions.

## **4. Conditional Types for Dynamic Typing**

Conditional types allow types to be chosen based on conditions.

- **What it is:** A type that depends on a condition (like a ternary operator in JavaScript). Often used with infer.  
  // Example: A type that extracts the return type of a function  
  type GetReturnType\<T\> = T extends (...args: any\[\]) =\> infer R ? R : any;  
    
  type SumFn = (a: number, b: number) =\> number;  
  type SumResult = GetReturnType\<SumFn\>; // Type: number  
    
  type VoidFn = () =\> void;  
  type VoidResult = GetReturnType\<VoidFn\>; // Type: void  
    
  // Example for a component that renders differently based on a prop  
  type ComponentProps\<T extends 'A' \| 'B'\> = T extends 'A' ? { dataA: string } : { dataB: number };  
    
  // MyComponent expects either { dataA: string } or { dataB: number } based on the 'type' prop  
  // function MyComponent\<T extends 'A' \| 'B'\>(props: { type: T } & ComponentProps\<T\>) { /\* ... \*/ }

- **Why it helps:** Enables highly dynamic and flexible type definitions, especially useful for advanced component patterns, higher-order components, or when you need to infer types from complex functions. It helps avoid overloads or excessive unions.

## **5. Typing lodash-es Functions**

When using lodash-es (our chosen utility library), you want to ensure its functions are type-safe.

- **What it is:** lodash-es comes with its own TypeScript definitions, so importing functions individually provides type safety out-of-the-box.  
  // Example: src/utils/helpers.ts (or where you use lodash-es)  
  import { get, map, filter, cloneDeep } from 'lodash-es';  
    
  interface SettingsConfig {  
  id: string;  
  value: any; // Can be any type, often a JSON object  
  path: string; // e.g., "ui.theme"  
  }  
    
  const settings: SettingsConfig\[\] = \[  
  { id: '1', value: 'dark', path: 'ui.theme' },  
  { id: '2', value: true, path: 'notifications.enabled' },  
  { id: '3', value: { x: 10, y: 20 }, path: 'position' },  
  \];  
    
  // Safely access nested properties  
  const theme = get(settings.find(s =\> s.path === 'ui.theme'), 'value', 'light');  
  // Type of 'theme' is 'any' if get's return type is not inferred strictly, but often string/boolean from context.  
  // If your JSONB is typed on the backend, you can narrow this.  
  // Use optional chaining for safety: settings.find(...)?.value  
    
  // Map over an array with type safety  
  const projectNames = map(settings, 'name'); // This example needs 'name' in SettingsConfig  
  // Assuming a Project\[\]:  
  interface ProjectSummary { id: string; name: string; }  
  const projects: ProjectSummary\[\] = \[{id:'p1', name:'ProjA'}, {id:'p2', name:'ProjB'}\];  
  const names = map(projects, 'name'); // names is string\[\]  
    
  // Deep cloning objects  
  const originalObject = { config: { value: 10 } };  
  const clonedObject = cloneDeep(originalObject);  
  clonedObject.config.value = 20; // originalObject.config.value is still 10

- **Why it helps:** Ensures that lodash-es functions are used with correct argument types and return values, maintaining end-to-end type safety even for common utility operations. The tree-shakable nature of lodash-es also helps keep bundle sizes down.
