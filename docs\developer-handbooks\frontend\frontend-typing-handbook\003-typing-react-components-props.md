# **Typing React Components & Props**

This guide explains how to effectively use TypeScript to type your React components and their props, ensuring type safety and clarity in our Ultimate Electrical Designer.

## **1. Typing Functional Components**

Functional components are the standard in modern React.

- **Basic Props Interface:** Define an interface or type for your component's props.  
  // src/components/common/ProjectCard.tsx  
  import React from 'react';  
    
  interface ProjectCardProps {  
  projectId: string;  
  projectName: string;  
  status: 'Active' \| 'Completed' \| 'Pending'; // Union type for specific statuses  
  lastUpdated: Date; // Use Date object for dates, or string for ISO format  
  onClick: (projectId: string) =\> void; // Event handler prop  
  }  
    
  const ProjectCard: React.FC\<ProjectCardProps\> = ({  
  projectId,  
  projectName,  
  status,  
  lastUpdated,  
  onClick  
  }) =\> {  
  return (  
  \<div className="p-4 border rounded-lg shadow-sm" onClick={() =\> onClick(projectId)}\>  
  \<h3 className="font-bold text-lg"\>{projectName}\</h3\>  
  \<p\>Status: {status}\</p\>  
  \<p\>Last Updated: {lastUpdated.toLocaleDateString()}\</p\>  
  \</div\>  
  );  
  };  
    
  export default ProjectCard;

- **React.FC\<Props\> or just (props: Props)?**

  - React.FC\<Props\> (or React.FunctionComponent\<Props\>) implicitly adds children prop and displayName.

  - Just (props: Props) requires you to explicitly add children?: React.ReactNode if you need it.

  - **Recommendation:** For our project, explicitly defining children (if needed) and using (props: Props) is often preferred as it's more explicit and avoids potential issues with implicit children types in older React versions or when strictly linting.

// Preferred explicit typing for clarity  
interface ButtonProps {  
onClick: () =\> void;  
label: string;  
children?: React.ReactNode; // Explicitly define if component can take children  
}  
  
const MyButton = ({ onClick, label, children }: ButtonProps) =\> {  
return \<button onClick={onClick}\>{label}{children}\</button\>;  
};

## **2. Defining Props: Required, Optional, Default Props**

- **Required Props:** All props defined in the interface are required by default.  
  interface RequiredProps {  
  title: string; // Must be provided  
  }

- **Optional Props:** Use ? to mark props as optional.  
  interface OptionalProps {  
  value: number;  
  unit?: string; // Optional unit  
  }

- **Props with Default Values:** For props that have default values, mark them as optional in the interface and assign a default value in the component's destructuring or within its body.  
  interface HeaderProps {  
  title: string;  
  showSettingsButton?: boolean; // Optional in type  
  }  
    
  const Header = ({ title, showSettingsButton = true }: HeaderProps) =\> {  
  return (  
  \<header\>  
  \<h1\>{title}\</h1\>  
  {showSettingsButton && \<button\>Settings\</button\>}  
  \</header\>  
  );  
  };

## **3. Typing Children (React.ReactNode)**

The children prop can be anything renderable by React.

- **React.ReactNode**: The most common and flexible type for children. It can be JSX elements, strings, numbers, booleans, null, undefined, or arrays of these.  
  import React from 'react';  
    
  interface CardProps {  
  title: string;  
  children: React.ReactNode; // Accepts any valid React child  
  }  
    
  const Card: React.FC\<CardProps\> = ({ title, children }) =\> {  
  return (  
  \<div className="card"\>  
  \<h2\>{title}\</h2\>  
  {children}  
  \</div\>  
  );  
  };  
    
  // Usage:  
  \<Card title="Circuit Details"\>  
  \<p\>This is a paragraph inside the card.\</p\>  
  \<span\>Another element.\</span\>  
  \</Card\>

## **4. Event Handlers**

Typing event handlers ensures you correctly handle event objects and their properties.

- **Common Event Types:** React provides specific event types:

  - React.MouseEvent\<HTMLButtonElement\>: For click events on buttons.

  - React.ChangeEvent\<HTMLInputElement\>: For input changes on input fields.

  - React.FormEvent\<HTMLFormElement\>: For form submission.

interface InputFieldProps {  
value: string;  
onChange: (event: React.ChangeEvent\<HTMLInputElement\>) =\> void;  
onBlur?: (event: React.FocusEvent\<HTMLInputElement\>) =\> void;  
}  
  
const InputField: React.FC\<InputFieldProps\> = ({ value, onChange, onBlur }) =\> {  
return (  
\<input type="text" value={value} onChange={onChange} onBlur={onBlur} /\>  
);  
};  
  
interface SubmitButtonProps {  
onClick: (event: React.MouseEvent\<HTMLButtonElement\>) =\> void;  
}  
  
const SubmitButton: React.FC\<SubmitButtonProps\> = ({ onClick }) =\> {  
return (  
\<button type="submit" onClick={onClick}\>Submit\</button\>  
);  
};

## **5. Leveraging shadcn/ui Component Types**

shadcn/ui components (built on Radix UI) provide their own types that you can extend or reference, making integration seamless.

- **Extending shadcn/ui Props:** Many shadcn/ui components expose their props, allowing you to add your own while retaining their base types.  
  // src/components/common/CustomButton.tsx  
  import { Button } from 'components/ui/button'; // Assuming shadcn/ui button  
  import { ButtonProps } from 'components/ui/button'; // Import its types  
    
  interface CustomButtonProps extends ButtonProps {  
  isLoading?: boolean;  
  icon?: React.ReactNode;  
  }  
    
  const CustomButton: React.FC\<CustomButtonProps\> = ({  
  isLoading,  
  icon,  
  children,  
  ...props  
  }) =\> {  
  return (  
  \<Button {...props} disabled={isLoading \|\| props.disabled}\>  
  {isLoading ? 'Loading...' : icon}  
  {children}  
  \</Button\>  
  );  
  };  
    
  export default CustomButton;

- **Why:** This approach ensures your custom components maintain compatibility with the underlying shadcn/ui props, reducing type errors and making it easier to integrate and style the base components.
