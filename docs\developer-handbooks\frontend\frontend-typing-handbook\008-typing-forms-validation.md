# **Typing Forms & Validation**

This guide provides best practices for typing forms and their associated validation logic in our Ultimate Electrical Designer's frontend, ensuring a robust and user-friendly experience.

## **1. Defining Form State Interfaces**

For forms, explicitly define the shape of your form data. This provides strong type checking as users interact with the fields.

- **What it is:** Create an interface or type alias that mirrors the structure of your form's state.  
  // src/modules/projects/components/ProjectCreateForm.tsx  
  interface ProjectFormData {  
  name: string;  
  description: string;  
  clientName: string;  
  startDate: string; // Often string for date inputs (ISO format)  
  isActive: boolean;  
  // ... other form fields  
  }  
    
  const initialFormData: ProjectFormData = {  
  name: '',  
  description: '',  
  clientName: '',  
  startDate: new Date().toISOString().split('T')\[0\], // YYYY-MM-DD  
  isActive: true,  
  };

- **Why it helps:** Ensures all form fields have a known type, preventing typos or incorrect assignments during form handling.

## **2. Typing Input Change Events**

React's event handlers provide type information for the DOM element they originate from.

- **What it is:** Use React.ChangeEvent\<HTMLInputElement \| HTMLTextAreaElement \| HTMLSelectElement\> for common input types.  
  // Inside ProjectCreateForm component  
  import React, { useState } from 'react';  
    
  const ProjectCreateForm: React.FC = () =\> {  
  const \[formData, setFormData\] = useState\<ProjectFormData\>(initialFormData);  
    
  const handleChange = (e: React.ChangeEvent\<HTMLInputElement \| HTMLTextAreaElement \| HTMLSelectElement\>) =\> {  
  const { name, value, type, checked } = e.target;  
  setFormData(prevData =\> ({  
  ...prevData,  
  \[name\]: type === 'checkbox' ? checked : value,  
  }));  
  };  
    
  // For a specific input if you prefer  
  const handleNameChange = (e: React.ChangeEvent\<HTMLInputElement\>) =\> {  
  setFormData(prev =\> ({ ...prev, name: e.target.value }));  
  };  
    
  return (  
  \<form\>  
  \<input  
  type="text"  
  name="name"  
  value={formData.name}  
  onChange={handleChange} // or handleNameChange  
  placeholder="Project Name"  
  /\>  
  \<textarea  
  name="description"  
  value={formData.description}  
  onChange={handleChange}  
  placeholder="Project Description"  
  /\>  
  \<input  
  type="checkbox"  
  name="isActive"  
  checked={formData.isActive}  
  onChange={handleChange}  
  /\>  
  {/\* ... other inputs \*/}  
  \</form\>  
  );  
  };

- **Why it helps:** Ensures you access the correct properties (value, checked, name, id, etc.) from the event target without TypeScript complaints, improving reliability.

## **3. Structuring and Typing Validation Errors**

Validation can happen both on the frontend and backend. Typing errors consistently is key.

- **What it is:** Define an interface for local form validation errors. This can mirror the backend's ErrorResponseSchema\['field_errors'\] for consistency.  
  // src/types/validation.ts (Example)  
  interface FormErrors {  
  \[key: string\]: string\[\]; // Field name -\> Array of error messages  
  }  
  // You might also have a global form status  
  type FormStatus = 'idle' \| 'submitting' \| 'success' \| 'error';

- **Frontend Validation Logic:**  
  // Inside ProjectCreateForm component (continued)  
  import { useState } from 'react';  
  import { useCreateProject } from 'modules/projects/hooks/useProjectDetails'; // Backend interaction  
  import { ErrorResponseSchema } from 'types/api'; // Backend error type  
    
  const ProjectCreateForm: React.FC = () =\> {  
  const \[formData, setFormData\] = useState\<ProjectFormData\>(initialFormData);  
  const \[localErrors, setLocalErrors\] = useState\<FormErrors\>({}); // Local validation errors  
  const { mutate: createProject, isLoading, error: backendError } = useCreateProject();  
  const \[formStatus, setFormStatus\] = useState\<FormStatus\>('idle');  
    
  const validateForm = (data: ProjectFormData): FormErrors =\> {  
  const errors: FormErrors = {};  
  if (!data.name.trim()) {  
  errors.name = \['Project name is required.'\];  
  }  
  if (data.description.length \> 500) {  
  errors.description = \['Description cannot exceed 500 characters.'\];  
  }  
  // ... more validation rules  
    
  return errors;  
  };  
    
  const handleSubmit = (e: React.FormEvent\<HTMLFormElement\>) =\> {  
  e.preventDefault();  
  setFormStatus('submitting');  
  setLocalErrors({}); // Clear previous local errors  
    
  const errors = validateForm(formData);  
  if (Object.keys(errors).length \> 0) {  
  setLocalErrors(errors);  
  setFormStatus('error');  
  return;  
  }  
    
  // If local validation passes, call backend  
  createProject(  
  { projectData: formData },  
  {  
  onSuccess: () =\> {  
  setFormStatus('success');  
  // Handle success (e.g., navigate, show toast)  
  },  
  onError: (apiError) =\> { // apiError is ErrorResponseSchema from useCreateProject  
  setFormStatus('error');  
  if (apiError.field_errors) {  
  // Merge backend field errors into local error state  
  setLocalErrors(prev =\> ({ ...prev, ...apiError.field_errors }));  
  }  
  // Show global error message via toast if needed  
  console.error("Backend error:", apiError.detail);  
  },  
  }  
  );  
  };  
    
  return (  
  \<form onSubmit={handleSubmit} className="space-y-4"\>  
  \<div\>  
  \<label htmlFor="name"\>Project Name:\</label\>  
  \<input type="text" name="name" value={formData.name} onChange={handleChange} /\>  
  {localErrors.name && \<p className="text-red-500"\>{localErrors.name.join(', ')}\</p\>}  
  \</div\>  
  {/\* ... other fields ... \*/}  
  {/\* Display global backend error \*/}  
  {formStatus === 'error' && backendError && !backendError.field_errors && (  
  \<p className="text-red-500"\>Form Submission Error: {backendError.detail}\</p\>  
  )}  
  \<button type="submit" disabled={isLoading \|\| formStatus === 'submitting'}\>  
  {isLoading ? 'Submitting...' : 'Save Project'}  
  \</button\>  
  \</form\>  
  );  
  };

- **Why it helps:** Provides a consistent structure for managing validation feedback to the user. By typing local errors and integrating them with backend ErrorResponseSchema, you ensure that all validation messages (both frontend and backend) are handled predictably.

## **4. Integration with Zod for Frontend Validation Schemas**

For more complex or shared validation logic, a schema validation library like Zod is highly beneficial.

- **What it is:** Zod allows you to define validation schemas for your data using a fluent API. These schemas can then be used to validate form inputs or API responses.  
  // src/schemas/frontend_validation.ts (Example Zod schema)  
  import { z } from 'zod';  
    
  export const projectFormSchema = z.object({  
  name: z.string().min(3, "Project name must be at least 3 characters").max(100, "Project name cannot exceed 100 characters"),  
  description: z.string().max(500, "Description cannot exceed 500 characters").optional(),  
  clientName: z.string().min(1, "Client name is required."),  
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}\$/, "Invalid date format (YYYY-MM-DD)"),  
  isActive: z.boolean(),  
  });  
    
  // You can also define an inferred type from the schema  
  export type ProjectFormSchemaType = z.infer\<typeof projectFormSchema\>;

- **Using Zod in Components:**  
  // Inside ProjectCreateForm component (with Zod)  
  import { useState } from 'react';  
  import { useCreateProject } from 'modules/projects/hooks/useProjectDetails';  
  import { ErrorResponseSchema } from 'types/api';  
  import { projectFormSchema, ProjectFormSchemaType } from 'schemas/frontend_validation'; // Our Zod schema  
  import { ZodError } from 'zod'; // Import ZodError  
    
  const ProjectCreateFormZod: React.FC = () =\> {  
  const \[formData, setFormData\] = useState\<ProjectFormSchemaType\>(initialFormData);  
  const \[localErrors, setLocalErrors\] = useState\<FormErrors\>({});  
  const { mutate: createProject, isLoading, error: backendError } = useCreateProject();  
  const \[formStatus, setFormStatus\] = useState\<FormStatus\>('idle');  
    
  const handleSubmit = (e: React.FormEvent\<HTMLFormElement\>) =\> {  
  e.preventDefault();  
  setFormStatus('submitting');  
  setLocalErrors({}); // Clear previous local errors  
    
  try {  
  // Validate with Zod  
  const validatedData = projectFormSchema.parse(formData);  
    
  // If validation passes, call backend  
  createProject(  
  { projectData: validatedData }, // validatedData is now type-safe  
  {  
  onSuccess: () =\> {  
  setFormStatus('success');  
  // Handle success  
  },  
  onError: (apiError) =\> {  
  setFormStatus('error');  
  if (apiError.field_errors) {  
  setLocalErrors(prev =\> ({ ...prev, ...apiError.field_errors }));  
  }  
  console.error("Backend error:", apiError.detail);  
  },  
  }  
  );  
  } catch (err) {  
  if (err instanceof ZodError) {  
  const newErrors: FormErrors = {};  
  err.errors.forEach(issue =\> {  
  // Zod errors provide path, map them to field_errors structure  
  const fieldName = issue.path.join('.');  
  if (fieldName) {  
  if (!newErrors\[fieldName\]) {  
  newErrors\[fieldName\] = \[\];  
  }  
  newErrors\[fieldName\].push(issue.message);  
  }  
  });  
  setLocalErrors(newErrors);  
  setFormStatus('error');  
  } else {  
  console.error("Unexpected validation error:", err);  
  setFormStatus('error');  
  }  
  }  
  };  
    
  return (  
  \<form onSubmit={handleSubmit} className="space-y-4"\>  
  {/\* ... input fields with name attributes matching schema ... \*/}  
  {/\* Local validation errors \*/}  
  {localErrors.name && \<p className="text-red-500"\>{localErrors.name.join(', ')}\</p\>}  
  {/\* Global backend error display \*/}  
  {formStatus === 'error' && backendError && !backendError.field_errors && (  
  \<p className="text-red-500"\>Form Submission Error: {backendError.detail}\</p\>  
  )}  
  \<button type="submit" disabled={isLoading \|\| formStatus === 'submitting'}\>  
  {isLoading ? 'Submitting...' : 'Save Project'}  
  \</button\>  
  \</form\>  
  );  
  };

- **Why it helps:** Zod provides powerful, reusable validation schemas. It enables shared validation logic between different parts of the frontend, or even with the backend if you use Zod on both ends (though our backend uses Pydantic). It helps ensure data integrity before sending to the backend and provides robust error mapping.
