# **Frontend Linting Guide: ESLint for TypeScript (Next.js & React)**

This guide provides a comprehensive overview and detailed instructions for configuring ESLint for our Ultimate Electrical Designer's frontend, ensuring consistent code quality, catching common errors, and enforcing best practices across our TypeScript, React, and Next.js codebase.

## **1. Introduction: Why ESLint for Our Frontend?**

ESLint is an essential static analysis tool that helps us enforce coding standards and identify potential problems early in the development cycle. For our TypeScript-based React/Next.js application, it offers several critical benefits:

- **Code Consistency:** Ensures all developers adhere to a unified coding style, making the codebase easier to read, understand, and navigate.

- **Early Bug Detection:** Catches common programming errors (e.g., unused variables, unreachable code, incorrect hook usage) before they lead to runtime issues.

- **Best Practice Enforcement:** Guides developers towards established best practices for React, Next.js, and TypeScript, improving code robustness and performance.

- **Improved Code Quality:** Reduces technical debt by maintaining a high standard of code hygiene.

- **Pre-commit Validation:** Integrated with pre-commit hooks, ESLint acts as a gatekeeper, preventing problematic code from even being committed.

## **2. Core Setup: Installation & Basic Configuration**

To use ESLint effectively with TypeScript, React, and Next.js, you need several packages.

### **2.1. Installation**

- **What:** Install ESLint itself, the TypeScript parser and plugin for ESLint, and specific configurations for React and Next.js. We'll also install eslint-config-prettier to handle conflicts with Prettier.  
  npm install --save-dev eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin \\  
  eslint-config-next eslint-plugin-react eslint-plugin-react-hooks \\  
  eslint-config-prettier eslint-plugin-prettier

- **Why:**

  - eslint: The core ESLint library.

  - @typescript-eslint/parser: Allows ESLint to understand TypeScript syntax.

  - @typescript-eslint/eslint-plugin: Provides TypeScript-specific linting rules.

  - eslint-config-next: Official ESLint configuration for Next.js projects, including built-in React rules and Next.js-specific optimizations.

  - eslint-plugin-react: General React-specific linting rules (often covered by eslint-config-next).

  - eslint-plugin-react-hooks: Enforces rules of hooks.

  - eslint-config-prettier: Turns off all ESLint rules that are unnecessary or might conflict with Prettier.

  - eslint-plugin-prettier: Runs Prettier as an ESLint rule (useful if you want Prettier issues to appear as ESLint errors).

### **2.2. Configuration File (.eslintrc.js)**

- **What:** Create a .eslintrc.js file at the root of your frontend project. This file is the central hub for all ESLint configurations.  
  // .eslintrc.js  
  module.exports = {  
  // Specifies the ESLint parser for TypeScript  
  parser: '@typescript-eslint/parser',  
  // Parser options for TypeScript  
  parserOptions: {  
  ecmaVersion: 'latest', // Allow parsing of modern ECMAScript features  
  sourceType: 'module', // Allows for the use of imports  
  ecmaFeatures: {  
  jsx: true, // Enable JSX for React  
  },  
  // Point ESLint to your tsconfig.json for full type-aware linting  
  project: './tsconfig.json',  
  tsconfigRootDir: \_\_dirname,  
  },  
  // Environment settings  
  env: {  
  browser: true, // Browser global variables  
  es2021: true, // ECMAScript 2021 global variables  
  node: true, // Node.js global variables and Node.js scoping  
  },  
  // Order matters here: Prettier configs should always be last  
  extends: \[  
  'eslint:recommended', // ESLint's base recommended rules  
  'plugin:@typescript-eslint/recommended', // TypeScript-specific recommended rules  
  'plugin:react/recommended', // React-specific recommended rules  
  'plugin:react-hooks/recommended', // Rules for React Hooks  
  'next/core-web-vitals', // Next.js recommended rules (includes React rules)  
  'prettier', // Turns off ESLint rules that conflict with Prettier  
  'plugin:prettier/recommended', // Runs Prettier as an ESLint rule and reports differences as errors  
  \],  
  // Plugins ESLint will use  
  plugins: \[  
  '@typescript-eslint',  
  'react',  
  'react-hooks',  
  'prettier',  
  \],  
  // Specific rule overrides or additions  
  rules: {  
  // --- General ESLint Rules ---  
  'no-console': \['warn', { allow: \['warn', 'error'\] }\], // Warn for console.log, allow warn/error  
  'no-unused-vars': 'off', // Turn off base rule, use TS-specific one below  
    
  // --- TypeScript ESLint Rules (@typescript-eslint plugin) ---  
  '@typescript-eslint/no-unused-vars': \['warn', { argsIgnorePattern: '^\_' }\], // Warn for unused vars, ignore if prefixed with \_  
  '@typescript-eslint/explicit-function-return-type': 'off', // Often too verbose, prefer inference  
  '@typescript-eslint/explicit-module-boundary-types': 'off', // Often too verbose for React components  
  '@typescript-eslint/no-explicit-any': 'warn', // Warn for 'any' usage, consider making it 'error' later  
  '@typescript-eslint/no-non-null-assertion': 'warn', // Warn for ! usage, consider making it 'error' later  
  '@typescript-eslint/no-floating-promises': 'error', // Important: Ensures promises are handled  
    
  // --- React Specific Rules (from eslint-plugin-react) ---  
  'react/react-in-jsx-scope': 'off', // Not needed for Next.js/React 17+  
  'react/prop-types': 'off', // Not needed with TypeScript  
  'react/self-closing-comp': \['error', { component: true, html: true }\], // Enforce self-closing tags  
    
  // --- React Hooks Specific Rules (from eslint-plugin-react-hooks) ---  
  'react-hooks/rules-of-hooks': 'error', // Enforce Rules of Hooks  
  'react-hooks/exhaustive-deps': 'warn', // Warn if useEffect/useCallback dependencies are missing  
    
  // --- Next.js Specific Rules (from eslint-config-next) ---  
  // 'next/no-img-element': 'error', // Example: Enforce Image component for optimization (often comes with next/core-web-vitals)  
    
  // --- Prettier Integration Rules ---  
  'prettier/prettier': \[  
  'error',  
  {  
  singleQuote: true,  
  trailingComma: 'es5',  
  printWidth: 100, // Match your Prettier config  
  tabWidth: 2,  
  semi: true,  
  },  
  \],  
  },  
  // Settings for plugins  
  settings: {  
  react: {  
  version: 'detect', // Automatically detect the React version  
  },  
  },  
  // Override rules for specific files or patterns (optional)  
  overrides: \[  
  {  
  files: \['\*.js', '\*.jsx'\], // Apply to JS/JSX files only  
  rules: {  
  // Disable TypeScript-specific rules for JS files if needed  
  '@typescript-eslint/no-var-requires': 'off',  
  },  
  },  
  {  
  files: \['\*\*/\*.spec.ts', '\*\*/\*.test.ts', '\*\*/\*.d.ts'\], // For test and declaration files  
  rules: {  
  '@typescript-eslint/no-explicit-any': 'off', // Allow 'any' in tests/declarations  
  '@typescript-eslint/no-non-null-assertion': 'off', // Allow ! in tests  
  'no-console': 'off', // Allow console.log in tests  
  },  
  },  
  \],  
  };

- **Why:** This detailed configuration combines recommended rules from ESLint, TypeScript, React, and Next.js, providing a robust baseline. The extends array is ordered specifically to ensure Prettier rules correctly override others. parserOptions.project enables powerful **type-aware linting**, which is crucial for TypeScript.

## **3. tsconfig.json for ESLint**

- **What:** ESLint's TypeScript parser needs to know about your tsconfig.json to perform type-aware linting (e.g., checking if a property exists on an object based on its actual type). Ensure your tsconfig.json correctly includes all source files.  
  // tsconfig.json (Key parts for ESLint)  
  {  
  "compilerOptions": {  
  // ... your existing compiler options ...  
  "lib": \["dom", "dom.iterable", "esnext"\], // Essential for browser environment  
  "allowJs": true,  
  "skipLibCheck": true,  
  "strict": true, // Crucial for robust TS linting  
  "forceConsistentCasingInFileNames": true,  
  "noEmit": true,  
  "esModuleInterop": true,  
  "module": "esnext",  
  "moduleResolution": "bundler", // Or "node" depending on your Next.js config  
  "resolveJsonModule": true,  
  "isolatedModules": true,  
  "jsx": "preserve",  
  "incremental": true,  
  "plugins": \[  
  {  
  "name": "next"  
  }  
  \],  
  "paths": {  
  "@/\*": \["./src/\*"\] // Important for module resolution in ESLint and Next.js  
  }  
  },  
  "include": \["next-env.d.ts", "\*\*/\*.ts", "\*\*/\*.tsx", ".next/types/\*\*/\*.ts"\],  
  "exclude": \["node_modules"\]  
  }

- **Why:** Without a proper tsconfig.json linked via parserOptions.project, ESLint cannot perform advanced type-aware rules (like checking for properties on an object only if it's not null or undefined). paths also help ESLint resolve your custom module aliases.

## **4. Tailoring Rules for Our Application**

We've already set a strong base, but specific rules reinforce our DRY and Modularity goals.

- **Rule Customization:** The rules section in .eslintrc.js allows you to customize the severity ('off', 'warn', 'error') or behavior of any rule.  
  // .eslintrc.js (Snippet from rules section)  
  rules: {  
  // ... existing rules ...  
    
  // --- Enforcing Modularity & DRY ---  
  'max-lines-per-function': \['warn', { max: 50, skipBlankLines: true, skipComments: true }\], // Keep functions concise  
  'max-depth': \['warn', 3\], // Limit nesting depth  
  'complexity': \['warn', 10\], // Keep cyclomatic complexity low  
  'no-duplicate-imports': 'error', // Prevent redundant imports from same module  
  '@typescript-eslint/no-shadow': 'error', // Prevent variable shadowing (can be confusing)  
    
  // --- React Specific (Beyond recommended) ---  
  'react/jsx-pascal-case': \['error', { allowAllCaps: true }\], // Enforce PascalCase for custom components  
  'react/no-array-index-key': 'warn', // Warn for array index as key (prefer unique IDs)  
  'react/destructuring-assignment': \['warn', 'always'\], // Enforce destructuring props/state  
    
  // --- Best Practices for Explicit Typing (Adjust severity as team matures) ---  
  // '@typescript-eslint/no-explicit-any': 'error', // Stricter: Disallow 'any' entirely  
  // '@typescript-eslint/no-non-null-assertion': 'error', // Stricter: Disallow '!' entirely  
  // '@typescript-eslint/typedef': \[ // Enforce type annotations for specific constructs  
  // 'error',  
  // {  
  // 'arrowParameter': true,  
  // 'variableDeclaration': true,  
  // 'parameter': true,  
  // 'propertyDeclaration': true,  
  // },  
  // \],  
  }

- **Why:** Tailoring rules helps maintain code quality specific to our project's needs. For instance, limiting function lines or complexity directly supports readability and testability. Strict typing rules, while initially verbose, significantly improve type safety over time.

## **5. ESLint with Prettier Integration**

ESLint and Prettier serve different purposes (linting vs. formatting). We configure them to work together without conflict.

- **What:** The eslint-config-prettier package turns off ESLint rules that might conflict with Prettier's formatting rules. eslint-plugin-prettier then integrates Prettier's formatting checks directly into the ESLint workflow.

- **Order of extends:**  
  // .eslintrc.js (Snippet from extends array)  
  extends: \[  
  // ... other configs ...  
  'prettier', // MUST BE LAST in extending ESLint rules  
  'plugin:prettier/recommended', // MUST BE LAST to run Prettier as an ESLint rule  
  \],

- **Why:** This order ensures that Prettier is the single source of truth for formatting. ESLint will only report formatting issues that Prettier itself would fix, preventing conflicting errors. eslint-config-prettier disables ESLint's formatting rules, and eslint-plugin-prettier then re-enables Prettier as an ESLint rule.

## **6. Ignoring Files (.eslintignore)**

- **What:** Create a .eslintignore file at the root of your project to tell ESLint which files or directories to skip during linting.  
  \# .eslintignore  
  .next/  
  node_modules/  
  build/  
  dist/  
  out/  
  coverage/  
  src/api/generated/ \# Ignore auto-generated API client files

- **Why:** Prevents ESLint from wasting time on generated files, build artifacts, or third-party code, which you don't control and shouldn't lint.

## **7. Running ESLint**

Integrate ESLint into your development workflow.

- **package.json Scripts:**  
  // package.json (Snippet)  
  {  
  "scripts": {  
  "lint": "eslint \\{app,components,modules,hooks,lib,services,utils,types}/\*\*/\*.{ts,tsx}\\ --cache",  
  "lint:fix": "eslint \\{app,components,modules,hooks,lib,services,utils,types}/\*\*/\*.{ts,tsx}\\ --fix --cache"  
  }  
  }

  - **npm run lint**: Checks for linting errors.

  - **npm run lint:fix**: Checks and automatically fixes fixable linting errors.

- **CLI Usage:** You can run ESLint directly from the command line for specific files.  
  npx eslint src/modules/projects/components/ProjectForm.tsx

- **Why:** Provides easy and consistent ways to run linting checks during development and in CI/CD pipelines. --cache speeds up subsequent runs.

## **8. IDE Integration**

- **What:** Install the ESLint extension for your IDE (e.g., "ESLint" extension for VS Code).

- **Why:** Provides real-time linting feedback directly in your editor as you type, highlighting errors and warnings and often offering quick-fixes, significantly boosting developer productivity.

## **9. Best Practices for ESLint Maintenance**

- **Regular Review:** Periodically review your ESLint rules as your project evolves or new best practices emerge.

- **Keep Dependencies Updated:** Regularly update ESLint and its plugins/parsers to benefit from new rules and bug fixes.

- **Address Warnings:** Don't ignore warnings. Treat them as potential issues that should be addressed or explicitly suppressed with comments (// eslint-disable-next-line).

- **Custom Rules (Advanced):** For highly specific project conventions not covered by existing rules, consider writing custom ESLint rules (advanced topic).

- **CI/CD Integration:** Ensure ESLint runs as part of your Continuous Integration pipeline to prevent unlinted code from being merged into the main branch.

By following this guide, our frontend codebase will maintain a high standard of quality, consistency, and robustness through effective ESLint configuration.
